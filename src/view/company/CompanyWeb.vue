<template>
  <div class="company-web">
    <!-- 顶部轮播图 -->
    <section class="hero-banner">
      <van-swipe
        class="banner-swipe"
        :autoplay="5000"
        indicator-color="white"
        :touchable="true"
        :show-indicators="true"
        :loop="true"
        :duration="500"
      >
        <van-swipe-item>
          <img
            src="@/assets/images/web/RewardBanner.png"
            :alt="'奖励banner'"
            class="banner-image"
          />
        </van-swipe-item>
        <van-swipe-item>
          <img
            src="@/assets/images/web/TeamBuildingBanner.png"
            :alt="'团建banner'"
            class="banner-image"
          />
        </van-swipe-item>
      </van-swipe>
    </section>

    <!-- 主要内容区域 -->
    <section class="main-content">
      <div class="container">
        <div class="content-layout">
          <div
            v-if="!showNewsDetail"
            style="display: flex; gap: 40px; align-items: flex-start"
          >
            <!-- 左侧导航 -->
            <aside class="sidebar">
              <div class="sidebar-header">
                <h3>LATEST</h3>
                <h3>NEWS</h3>
                <div style="display: flex; align-items: center">
                  <div class="divider"></div>
                  <div class="dot"></div>
                </div>
                <h2>最新动态</h2>
              </div>
              <nav class="sidebar-nav">
                <div
                  class="nav-item"
                  :class="{ active: activeTab === item.key }"
                  v-for="item in navItems"
                  :key="item.key"
                  @click="switchTab(item.key)"
                >
                  {{ item.label }}
                </div>
              </nav>
            </aside>

            <!-- 右侧内容 -->
            <main class="content-area">
              <div class="news-list">
                <div
                  class="news-item"
                  v-for="(news, index) in currentNewsList"
                  :key="index"
                  @click="clickNewsItem(news)"
                >
                  <div class="left">
                    <div class="news_date">{{ news.date }}</div>
                    <div class="news_year">{{ news.year }}</div>
                    <div class="news_underline" />
                  </div>
                  <div class="right">
                    <div class="news_title">{{ news.title }}</div>
                    <div class="news_summary">{{ news.summary }}</div>
                  </div>
                </div>
              </div>
            </main>
          </div>
          <div v-else>
            <div class="detail-nav">
              <span class="back" @click="showNewsDetail = false">最新动态</span>
              > {{ currentNews.summary }}
            </div>
            <div style="display: flex; gap: 40px; align-items: flex-start">
              <div class="left">
           
                <div
                  class="news-item"
                  style="margin-bottom: 10px;"
                  v-for="(news, index) in currentNewsList"
                  :key="index"
                  @click="switchNews(news)"
                  :class="{ activeNewItem : currentNews.title === news.title }"
                >
                  <div class="left">
                    <div class="news_date">{{ news.date }}</div>
                    <div class="news_year">{{ news.year }}</div>
                    <div class="news_underline" />
                  </div>
                  <div class="right">
                    <div class="news_title">{{ news.title }}</div>
                    <div class="news_summary">{{ news.summary }}</div>
                  </div>
                  <van-icon v-if="currentNews.title === news.title" name="play" class="play-icon" size="50" color="#E7EDFF"/>
                </div>
              </div>
              <div class="right">
                <iframe :src="currentNews.url" frameborder="0" class="iframe"></iframe>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 底部信息 -->
    <footer class="company-footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-left">
            <p>版权所有 © fushangyunfu.com</p>
            <p>广州福尚云科技研发有限公司</p>
            <p>
              <a href="https://beian.miit.gov.cn/" target="_blank"
                >粤ICP备 2024197431号-2</a
              >
            </p>
            <p>增值电信业务经营许可证：粤B2-20241521</p>
            <p><EMAIL></p>
          </div>
          <div class="footer-right">
            <div class="contact-info">
              <h4>专属服务热线</h4>
              <p>周一至周日(09:00-22:00)</p>
              <p class="phone">************</p>
            </div>
            <div class="qr-code">
              <img src="@/assets/images/mobile/qrcode2.jpg" alt="二维码" />
            </div>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { newsData } from "@/config/newsConfig.js";

// 当前激活的标签
const activeTab = ref("news");
const showNewsDetail = ref(false);
const currentNews = ref(null);

// 导航菜单
const navItems = ref([
  { key: "news", label: "公司新闻" },
  { key: "notice", label: "公告通知" },
  { key: "recruitment", label: "招聘通道" },
]);

// 当前显示的新闻列表
const currentNewsList = computed(() => {
  return newsData[activeTab.value] || [];
});

// 切换标签
const switchTab = (tabKey) => {
  activeTab.value = tabKey;
};

// 点击新闻项
function clickNewsItem(news) {
  currentNews.value = news;
  showNewsDetail.value = true;
}

// 切换新闻项
function switchNews(news){
  currentNews.value = news;
}

</script>

<style scoped lang="scss">
.company-web {
  width: 100%;
  min-height: 100vh;
  background: #f8f9fa;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  // 顶部轮播图
  .hero-banner {
    width: 100%;
    height: 400px;
    overflow: hidden;

    .banner-swipe {
      height: 100%;

      .van-swipe-item {
        height: 100%;
      }

      .banner-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        user-select: none; // 防止图片被选中
        -webkit-user-drag: none; // 防止图片被拖拽
      }

      // 指示器样式优化
      :deep(.van-swipe__indicators) {
        bottom: 20px;

        .van-swipe__indicator {
          background-color: rgba(255, 255, 255, 0.5);

          &.van-swipe__indicator--active {
            background-color: #007bff;
          }
        }
      }
    }
  }

  // 主要内容区域
  .main-content {
    padding: 40px 0;

    .content-layout {
    }

    // 左侧导航
    .sidebar {
      flex: 0 0 280px;

      .sidebar-header {
        padding: 30px 0;

        h3 {
          font-size: 24px;
          color: #999;
          margin: 0 0 5px 0;
          font-weight: normal;
        }

        .divider {
          width: 40px;
          height: 4px;
          background: #007bff;
          margin: 10px 0;
          border-radius: 4px;
        }

        .dot {
          background: #007bff;
          width: 4px;
          height: 4px;
          margin-left: 2px;
          border-radius: 2px;
        }

        h2 {
          font-size: 24px;
          color: #333;
          margin: 0;
          font-weight: bold;
        }
      }

      .sidebar-nav {
        .nav-item {
          padding: 10px 4px;
          cursor: pointer;
          color: #333;
          font-size: 16px;
          margin-bottom: 20px;
          width: fit-content;

          &:hover {
            color: #007bff;
          }

          &.active {
            color: #007bff;
            border-bottom: 2px solid #007bff;
            font-weight: 600;
            position: relative;
          }

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    // 右侧内容
    .content-area {
      flex: 1;
      .news-list {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(410px, 1fr));
        gap: 20px;
      }
    }

    .detail-nav {
      margin-bottom: 30px;

      .left{}

      .right{
        flex:1;
      }

      .back {
        color: #007bff;
        text-decoration: underline;
      }

    }
  }

  // 底部信息
  .company-footer {
    background: #f8f9fa;
    padding: 40px 0;
    border-top: 1px solid #e5e5e5;

    .footer-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: 40px;

      .footer-left {
        flex: 1;

        p {
          margin: 5px 0;
          font-size: 14px;
          color: #666;
          line-height: 1.5;

          a {
            color: #007bff;
            text-decoration: none;

            &:hover {
              text-decoration: underline;
            }
          }
        }
      }

      .footer-right {
        display: flex;
        align-items: center;
        gap: 30px;

        .contact-info {
          text-align: right;

          h4 {
            font-size: 16px;
            color: #333;
            margin: 0 0 10px 0;
          }

          p {
            margin: 5px 0;
            font-size: 14px;
            color: #666;

            &.phone {
              font-size: 18px;
              font-weight: bold;
              color: #007bff;
            }
          }
        }

        .qr-code {
          img {
            width: 80px;
            height: 80px;
            border-radius: 8px;
          }
        }
      }
    }
  }
}

.news-item {
  display: flex;
  flex-direction: row;
  padding: 24px 40px;
  width: 410px;
  height: 135px;
  background: white;
  border-radius: 8px;
  position: relative;

  .left {
    display: inline-block;
    margin-right: 32px;
    .news_date {
      font-weight: bold;
      font-size: 23px;
      color: #000000;
      line-height: 23px;
      text-align: left;

      &.active {
        color: #4274FF;
      }
    }

    .news_year {
      font-size: 14px;
      color: #999999;
      line-height: 14px;
      text-align: left;
      margin-top: 6px;
    }

    .news_underline {
      width: 55px;
      height: 2px;
      background: #aaaaaa;
      margin-top: 27px;
      border-radius: 4px;
    }
  }

  .right {
    flex: 1;
    .news_title {
      font-weight: 600;
      font-size: 14px;
      color: #333333;
      line-height: 18px;
    }
    .news_summary {
      font-weight: 400;
      font-size: 12px;
      color: #999999;
      line-height: 15px;
      margin-top: 16px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
  }
  .play-icon{
    position:absolute;
    right: -30px;
    top: 50%;
    transform: translateY(-50%);
  }

}

.activeNewItem{
  background: #E7EDFF;

  .news_date {
    color: #4274FF !important;
  }
  .news_year{
    color: #4274FF !important;
  }
  .news_underline{
    background: #4274FF !important;
  }
}

</style>
